{"configurations": [{"name": "C#: Hca.WebHost", "type": "dotnet", "request": "launch", "projectPath": "${workspaceFolder}\\Hca.WebHost\\Hca.WebHost.csproj", "launchConfigurationId": "TargetFramework=;Hca.WebHost"}, {"name": "C#: Hca.WebHost (Hot Reload)", "type": "node", "request": "launch", "program": "dotnet", "args": ["watch", "run", "--project", "${workspaceFolder}/Hca.WebHost/Hca.WebHost.csproj"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "preLaunchTask": "build"}]}