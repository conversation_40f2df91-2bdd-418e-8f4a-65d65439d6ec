{"configurations": [{"name": "C#: Hca.WebHost", "type": "dotnet", "request": "launch", "projectPath": "${workspaceFolder}\\Hca.WebHost\\Hca.WebHost.csproj", "launchConfigurationId": "TargetFramework=;Hca.WebHost"}, {"name": "C#: Hca.WebHost (Hot Reload)", "type": "coreclr", "request": "launch", "preLaunchTask": "watch", "program": "${workspaceFolder}/Hca.WebHost/bin/Debug/net8.0/Hca.WebHost.dll", "args": [], "cwd": "${workspaceFolder}/Hca.WebHost", "console": "integratedTerminal", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}}]}