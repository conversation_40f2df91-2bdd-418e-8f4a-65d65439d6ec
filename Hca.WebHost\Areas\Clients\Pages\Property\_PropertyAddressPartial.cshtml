@model Hca.WebHost.Areas.Clients.Pages.Property.PropertyIndexModel
@{
    var sites = Model.Sites?.Select(prop => new SelectListItem(prop.SiteName, prop.Id.ToString())).ToList();
    sites.Insert(0, new SelectListItem("", ""));

    var photoUrl = await Model.GetPhotoUrl(Model.Property);
}

@if (!Model.InEditMode)
{
    <div class="row">
        <div class="col-6">
            <div class="card card-default p-3">
                @if (string.IsNullOrWhiteSpace(Model.Property.ImageBlobName))
                {
                    <p>No photo available</p>
                }
                else
                {
                    <modalImage imageUrl="@photoUrl" altText="Property Photo"></modalImage>
                }
            </div>
        </div>
        <div class="col-6">
            <div class="card card-default p-3">
                <partial name="_Map" model="new _MapModel()" />
            </div>
        </div>
    </div>
}

<div class="card card-default">
    @if (Model.InEditMode)
    {
        <div class="card-header">
            <small class="text-muted">EDIT PROPERTY</small>
        </div>
    }
    <form method="post" action="/properties/@Model.Property.Id/address" id="propertyAddressForm">
        <div class="card-body">
            @if (Model.Property.NextInspection.HasValue)
            {
                <input value="@(Model.Property.NextInspection.Value.ToString("dd-MM-yyyy"))"
                       asp-for="Property"
                       read-only="true"
                       type="text"
                       row-label="Next Inspection Due" />
            }

            <input type="hidden" asp-for="Client.Id" />
            <input type="hidden" asp-for="Property.Id" />

            <input type="text" asp-for="Property.PropertyCode" row-label="Property Code" />
            <input type="text" asp-for="Property.BuildingName" row-label="Building Name" />
            <input type="text" asp-for="Property.BuildingNumber" row-label="Building Number" />
            <input type="text" asp-for="Property.Unit" row-label="Unit" />
            <input type="text" asp-for="Property.Floor" row-label="Floor" />

            <select asp-for="Property.SiteId"
                    row-label="Site"
                    asp-items="sites" />

            <partial name="../_AddressFieldsPartial"
                     for="Address"
                     view-data='new ViewDataDictionary(ViewData) { { "readOnly", Model.Property.SiteId.HasValue } } ' />
        </div>
        <save-cancel-footer></save-cancel-footer>
    </form>
</div>
<script>

    $("#propertyAddressForm").submit(function (e) {
        e.preventDefault();

        var frm = this;
        var callback = (location) => {
            if (location) {
                $('#Address_Lat').val(location.lat);
                $('#Address_Lon').val(location.lng);
            }

            frm.submit();
        };

        findCoordinates({
            success: callback,
            fail: callback,
        });

        return false;
    });

</script>