@page "/clients/{clientId:guid}/properties/{id}"
@model Hca.WebHost.Areas.Clients.Pages.ClientPropertyModel
@{
    Layout = "_LayoutClient";
    ViewData["Client"] = Model.Client;

    var isNew = Model.Id.ToLower() == "new";
    var breadcrumbName = isNew ? "New" : Model.Property.PropertyCode;
    var heading = isNew ? "Add New Property" : "Edit Property";
    var yesNoItems = new[] { new SelectListItem("No", "false"), new SelectListItem("Yes", "true") };

    var floors = Model.Floors?.Select(prop => new SelectListItem(prop.DisplayText, prop.DisplayText)).ToList() ?? new List<SelectListItem>();
    floors.Insert(0, new SelectListItem("", ""));

    var sites = Model.Sites?.Select(prop => new SelectListItem(prop.SiteName, prop.Id.ToString())).ToList() ?? new List<SelectListItem>();
    sites.Insert(0, new SelectListItem("", ""));

    ViewData["showMap"] = !isNew;
}

<div class="row">
    <div class="col">
        <form method="post" id="propertyForm">
            <div class="card card-default">
                <div class="card-header d-flex align-items-center">
                    <div class="d-flex justify-content-center col">
                        <div class="h4 m-0 text-center">@heading</div>
                    </div>
                </div>
                <div class="card-body">
                    @if (isNew && !Model.IsSiteProperty.HasValue)
                    {
                        <select asp-for="IsSiteProperty"
                            row-label="Does this property belong to a site?"
                            asp-items="yesNoItems" />
                        <input type="submit" class="btn btn-success" value="Next" />
                    }
                    else
                    {
                        <input type="hidden" asp-for="Property.Id" />
                        <input type="hidden" asp-for="Property.AddressId" />

                        <input asp-for="Property.PropertyCode" row-label="Property Code" />
                        <select asp-for="Property.Floor" row-label="Floor" asp-items="floors" />
                        <input asp-for="Property.Unit" row-label="Unit (number only)" />
                        <input asp-for="Property.BuildingName" row-label="Building Name" />
                        <input asp-for="Property.BuildingNumber" row-label="Building Number" />

                        @if (Model.IsSiteProperty.Value)
                        {
                            <select asp-for="Property.SiteId"
                                row-label="Site"
                                asp-items="sites" />
                        }

                        @if (Model.Property.SiteId.HasValue)
                        {
                            <partial name="_AddressFormPartial"
                             for="Sites.SingleOrDefault(s => s.Id == Model.Property.SiteId).Address"
                             view-data='new ViewDataDictionary(ViewData) { { "readOnly", true } }' />
                        }
                        else if (!Model.IsSiteProperty.Value)
                        {
                            <partial name="_AddressFormPartial" for="Address" />
                        }
                    }
                </div>
                @if (Model.IsSiteProperty.HasValue)
                {
                    <div class="card-footer">
                        <button class="btn" id="btnCancelNewLocation">Cancel</button>
                        <input type="submit" class="btn btn-success" value="Save" />
                    </div>
                }
            </div>
            <input type="hidden" asp-for="IsSiteProperty" />
        </form>
    </div>
</div>

@section scripts{
    <partial name="_MapScripts" model="new _MapScriptsModel(Model.Property, Model.Address)" />

    <script>
        $("#propertyForm").submit(function (e) {
            e.preventDefault();

            var frm = this;
            var callback = () => { frm.submit(); };

            findCoordinates({
                success: callback,
                fail: callback,
            });

            return false;
        });

        $(() => {
            drawBreadcrumb([
                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { url: '@Urls.ClientProperties(Model.Client.Id)', text: 'Properties' },
                { text: '@breadcrumbName' }]);
        });
    </script>
}